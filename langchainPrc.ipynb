from dotenv import load_dotenv
load_dotenv(".env", override=True)

from langchain.chat_models import init_chat_model
llm = init_chat_model("gemini-2.5-pro", model_provider="google_genai")

from langgraph.graph import MessagesState
from typing import Literal
from langgraph.types import Command

class State(MessagesState):
    # We can add a specific key to our state for the email input
    post_input: dict
    classification_decision: Literal["ignore", "notify", "take_action"]


from pydantic import BaseModel, Field

class ProjectSchema(BaseModel):
    """Analyze the project"""
    
    reasoning: str = Field(
        description="Step-by-step reasoning behind the classification."
    )
    project_name: str = Field(
        description="The project name."
    )
    funding: int = Field(
        description="Function of the projet "
    )
    Rating: int = Field(
        description="Rating of the project"
    )
    project_type: str = Field(
        description="Type of the project"
    )
    project_category : str = Field(
        description="Category of the project"
    )
    

class RouterSchema(BaseModel):
    """ Analyze the post and route it according to its content."""

    reasoning: str = Field(
        description="Step-by-step reasoning behind the classification."
    )
    classification: Literal["ignore", "notify", "take_action"] = Field(
        description="The classification of an post: 'ignore' for irrelevant post, "
        "'notify' for important information that doesn't require response, "
        "'take_action' for post that requires a action like to create a task, create a page",
    )
    
    
llm_router = llm.with_structured_output(RouterSchema)

from TelegramAi.prompts import triage_system_prompt, default_background, default_triage_instructions , triage_user_prompt, AGENT_TOOLS_PROMPT, default_response_preferences, agent_system_prompt
from rich.markdown import Markdown
from langgraph.graph import END

def triage_router(state: State) -> Command[Literal["action_agent", "__end__"]]:
    """Analyze post content to decide if we should create a task, notify, or ignore."""
    
    # Parse the post input
    post_input = state["post_input"]
    text = post_input.get("text", "")
    metadata = post_input.get("metadata", {})
    system_prompt = triage_system_prompt.format(
        triage_instructions=default_triage_instructions
    )
    
    user_prompt = triage_user_prompt.format(
        metadata=metadata,
        post_thread=text
    )
    
    # Run the router LLM
    result = llm_router.invoke(
        [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": text},
        ]
    )
    print(result)
    # Decision
    if result.classification == "take_action":
        print("📧 Classification: Take Action - This post requires to create a task")
        goto = "action_agent"
        update = {
            "messages": [
                {
                    "role": "user",
                    "content": f"Take action for this post: \n\n{user_prompt}",
                }
            ],
            "classification_decision": result.classification,
        }
    
    elif result.classification == "ignore":
        print("🚫 Classification: IGNORE - This post can be safely ignored")
        goto = END
        update =  {
            "classification_decision": result.classification,
        }
    
    elif result.classification == "notify":
        print("🔔 Classification: NOTIFY - This post contains important information")
        goto = END
        update = {
            "classification_decision": result.classification,
        }
    else:
        raise ValueError(f"Invalid classification: {result.classification}")
    return Command(goto=goto, update=update)


msg = """
🔈 Cysic Airdrop — Social Task Update ✔️

⏱️ In 5 hours: They remove the Twitter task
🗓 In 17 hours: auto-complete any stuck Social Tasks for affected users

🔗 Link : https://app.cysic.xyz

If you Haven't Joined Join now Drop your Code Below - https://x.com/cryptoimsny2/status/1950840964840337832

——

🆕 New users can aslo join

"""

triage_router({"post_input": {"text": msg, "metadata": {}}})

from langchain.tools import tool
from pydantic import BaseModel

@tool
def create_task(project: str, task_name: str, priority: str, due_date: str, *args) -> str:
    """Create task for a specific project."""
    # Placeholder response - in real app would send email
    return f"Created task for {project} project. and task is {task_name}, priority is {priority} and due date is {due_date} other args are {args}"

@tool
def create_page(project: str, *args) -> str:
    """Create page for a specific project."""
    # Placeholder response - in real app would send email
    return f"Created page for {project} project. other args are {args}"

@tool
def notify_user(message: str, urgency: str) -> str:
    """Notify the user about important updates or task creation."""
    # Placeholder response - in real app would send email
    return f"User notified with message: {message} and urgency: {urgency}"

@tool
class Done(BaseModel):
      """E-mail has been sent."""
      done: bool
      
      

tools = [create_task, create_page, notify_user, Done]
tools_by_name = {tool.name: tool for tool in tools}

model_with_tools = llm.bind_tools(tools, tool_choice="any", parallel_tool_calls=False)

def llm_call(state: State):
    """LLM decides whether to call a tool or not"""
    output = {
        "messages": [
            model_with_tools.invoke(
                [
                    {"role": "system", "content": agent_system_prompt.format(
                        tools_prompt=AGENT_TOOLS_PROMPT,
                        background=default_background,
                        response_preferences=default_response_preferences,
                        )
                    },
                    
                ]
                + state["messages"]
            )
        ]
    }
    return output

llm_call({"post_input": {"text": msg, "metadata": {}}, "messages": []})

def tool_handler(state: State):
    """Performs the tool call."""

    # List for tool messages
    result = []
    
    # Iterate through tool calls
    for tool_call in state["messages"][-1].tool_calls:
        # Get the tool
        tool = tools_by_name[tool_call["name"]]
        # Run it
        observation = tool.invoke(tool_call["args"])
        # Create a tool message
        result.append({"role": "tool", "content" : observation, "tool_call_id": tool_call["id"]})
    
    # Add it to our messages
    return {"messages": result}

def should_continue(state: State) -> Literal["tool_handler", "__end__"]:
    """Route to tool handler, or end if Done tool called."""
    
    # Get the last message
    messages = state["messages"]
    last_message = messages[-1]
    print("Should continue is calling")
    # Check if it's a Done tool call
    if last_message.tool_calls:
        for tool_call in last_message.tool_calls: 
            if tool_call["name"] == "Done":
                return END
            else:
                return "tool_handler"

from langgraph.graph import StateGraph, START, END
from email_assistant.utils import show_graph

# Build workflow
overall_workflow = StateGraph(State)

# Add nodes
overall_workflow.add_node("llm_call", llm_call)
overall_workflow.add_node("tool_handler", tool_handler)

# Add edges
overall_workflow.add_edge(START, "llm_call")
overall_workflow.add_conditional_edges(
    "llm_call",
    should_continue,
    {
        "tool_handler": "tool_handler",
        END: END,
    },
)
overall_workflow.add_edge("tool_handler", "llm_call")

# Compile the agent
agent = overall_workflow.compile()

overall_workflow = (
    StateGraph(State)
    .add_node(triage_router)
    .add_node("action_agent", agent)
    .add_edge(START, "triage_router")
).compile()

from IPython.display import Image, display

try:
    display(Image(overall_workflow.get_graph().draw_mermaid_png()))
except Exception:
    # This requires some extra dependencies and is optional
    pass

post = {"text": """

 Opensea Airdrop — New tasks are Live ✔️

🔗 Link — https://opensea.io/rewards

1️⃣. Buy NFT from a Verified Collection on any chain [ Minimum $5 USD ]
✨  50 XP 

🖼 NFT Collection [ Base Chain - Cost $17 ]  — https://opensea.io/collection/dxterminal

🟢 Buy this NFT 
🟢 Claim XP 
🟢 Sell NFT 
✅ Done 

———

❗️ Once you Complete 1st Task and Claim XP now you can Complete 2nd Task

2️⃣. Go to a Gaming NFT Branded Collection Page
✨  50 XP 

🟢 Click on the Gaming NFT [ Dxterminal NFT ]
🟢 Go to the About Section and Scroll Down until you See Complete Voyage
❗️ If you are not able to click on Complete Voyage Try to Zoom out a little on Web ]
🟢 Claim XP 
✅ Done 

———

3️⃣. Complete a token swap on any chain [ Minimum $10 USD ]
✨  100 XP 

🟢 Swap on any chain [ Minimum $10 USD ] 
➡️ Do it on Base or unichain [ For Future Airdrop ]
🟢 Claim XP 
🟢 Sell your Tokens 
✅ Done

———

🆕 Now for New users
""",
"metadata": {}}

config = {"configurable": {"thread_id": "1"}}

response = overall_workflow.invoke({"post_input": post}, config)


for m in response["messages"]:
    m.pretty_print()

from main import get_client, TeleBot
import asyncio
from typing import Optional, List, Dict, Any
from datetime import datetime
import re

def extract_telegram_message(message) -> Dict[str, Any]:
    # Extract the main text
    text = message.message or ""
    
    # Build metadata dictionary with all the extra stuff
    metadata = {
        "date": message.date,
        "is_forwarded": bool(message.fwd_from),
        "is_post": message.post,
        "from_channel": str(message.peer_id.channel_id) if message.peer_id else None,
        "message_id": message.id,
        "all_links": re.findall(r'https?://\S+', text),
    }
    
    # Add media/webpage info if it exists
    if message.media and getattr(message.media, 'webpage', None):
        webpage = message.media.webpage
        metadata.update({
            "media_url": getattr(webpage, 'url', None),
            "link_preview_title": getattr(webpage, 'title', None),
            "link_preview_description": getattr(webpage, 'description', None),
            "link_preview_site": getattr(webpage, 'site_name', None),
        })
    
    # Add forward info if message is forwarded
    if message.fwd_from:
        metadata["forward_info"] = {
            "from_id": str(message.fwd_from.from_id) if message.fwd_from.from_id else None,
            "from_name": message.fwd_from.from_name,
            "date": message.fwd_from.date,
        }
    
    # Extract bold entities
    """
    bold_entities = []
    if hasattr(message, 'entities') and message.entities:
        for entity in message.entities:
            if entity.__class__.__name__ == "MessageEntityBold":
                offset = entity.offset
                length = entity.length
                bold_entities.append(text[offset:offset+length])
    
    if bold_entities:
        metadata["bold_entities"] = bold_entities
    """
    
    # Return as plain dict
    return {
        "text": text,
        "metadata": metadata
    }

